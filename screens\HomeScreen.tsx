import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Modal from 'react-native-modal';
import { requestNotifications } from 'react-native-permissions';
import Timeline from 'react-native-timeline-flatlist';
import CheckingNotificationsIcon from '../assets/images/checking-notifications.svg';
import PlusIcon from '../assets/images/circle-plus-solid.svg';
import { AlertModel } from '../common/AlertModel';
import AlertListItem from '../components/AlertListItem';
import AlertModal from '../components/AlertModel/AlertModal';
import Center from '../components/Center';
import EnrollMFAModal from '../components/EnrollMFAModal';
import ErrorMessage from '../components/ErrorMessage';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

type Props = {
  navigation: HomeScreenNavigationProp;
  route: any;
};

const HomeScreen: React.FC<Props> = ({navigation, route}) => {
  const {themeStyles, theme} = useTheme();
  const {home, clearHome} = useRefresh();
  const [refreshing, setRefreshing] = useState(false);
  const [timelineData, setTimelineData] = useState<AlertModel[]>([]);
  const [allAlertData, setAllAlertData] = useState<AlertModel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [outstandingAlerts, setOutstandingAlerts] = useState<AlertModel[]>([]);
  const [categoryName, setCategoryName] = useState<string>('');
  const [alertModalAvailable, setAlertModalAvailable] = useState<boolean>(true);
  const [showNotificationsModal, setShowNotificationsModal] =
    useState<boolean>(false);
  const [showFeedImage, setShowFeedImage] = useState<boolean>();
  const [showEnrollMFAModal, setShowEnrollMFAModal] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [currentAlertIndex, setCurrentAlertIndex] = useState(0);
  const [selectedFilter, setSelectedFilter] = useState<string>('all'); // 'all', '1day', '7days', '30days', '90days'
  const [showFilterDropdown, setShowFilterDropdown] = useState<boolean>(false);
  const [showPendingAlert, setShowPendingAlert] = useState<boolean>(false);
  const [pendingAlertData, setPendingAlertData] = useState<any>(null);

  const {
    getAlerts,
    getOutstandingAlerts,
    getAboutMessage,
    registerDevice,
    hideAboutMessage,
    saveUserSetting,
    getUserSettings,
  } = useApi();

  const windowWidth = Dimensions.get('window').width;
  const iconSize = windowWidth * 0.4;
  const plusIconSize = Math.min(windowWidth * 0.09, 65);
  const plusIconButtonSize = Math.min(windowWidth * 0.13, 100);
  const plusIconButtonBorderRadius = Math.min(windowWidth * 0.03, 30);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  // Always use 'all' as the default filter
  const loadDefaultFilter = async () => {
    try {
      setSelectedFilter('all');
    } catch (err) {
      setSelectedFilter('all');
    }
  };

  useEffect(() => {
    setIsLoading(true);
    setupNotifications();
    loadDefaultFilter().then(() => {
      fetchAboutMessage();
      fetchData(); // fetchData will handle setIsLoading(false) in its finally block
    });

    // Set up the countdown interval
    intervalRef.current = setInterval(updateCountdown, 1000);
    // Clean up the interval on component unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    setAlertModalAvailable(true);
  }, [route]);

  useEffect(() => {
    if (home) {
      setIsLoading(true);
      fetchData();
      getOutStandingAlerts();
      clearHome();
    }
  }, [home]);

  // Helper function to calculate time left for an alert
  const calculateTimeLeft = (alert: any) => {
    const now = new Date().getTime();
    const nextAlertDate = new Date(alert.nextAlertDate);
    const expiryDate = new Date(alert.expiryDate).getTime();

    // Validate dates
    if (isNaN(nextAlertDate.getTime()) || isNaN(expiryDate)) {
      return 'Invalid date';
    }

    // Check if expiry date has passed
    if (expiryDate < now) {
      return 'Expired';
    }

    let targetDate;

    // If defaultAlertTime is 0, always calculate from expiry date
    if (alert.defaultAlertTime === 0) {
      targetDate = expiryDate;
    } else {
      // Check if nextAlertDate is in the future
      if (nextAlertDate.getTime() > now) {
        // Calculate if there's enough time between nextAlertDate and expiryDate for defaultAlertTime
        const nextAlertPlusDefault = new Date(nextAlertDate);
        nextAlertPlusDefault.setDate(nextAlertPlusDefault.getDate() + alert.defaultAlertTime);

        if (nextAlertPlusDefault.getTime() > expiryDate) {
          // Not enough time for full cycle, countdown to expiry date
          targetDate = expiryDate;
        } else {
          // Enough time for full cycle, countdown to next alert date
          targetDate = nextAlertDate.getTime();
        }
      } else {
        // nextAlertDate has passed, calculate from expiry date
        targetDate = expiryDate;
      }
    }

    const timeLeft = targetDate - now;

    if (timeLeft <= 0) {
      return 'Expired';
    }

    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    if (days > 0) {
      return days > 1 ? `${days} days to go` : `${days} day to go`;
    } else if (hours > 0) {
      return `${hours} ${hours > 1 ? 'hours' : 'hour'}`;
    } else if (minutes > 0) {
      // Show both minutes and seconds when less than 5 minutes
      if (minutes < 5 && seconds > 0) {
        return `${minutes} min ${seconds} sec`;
      } else {
        return `${minutes} min`;
      }
    } else if (seconds > 0) {
      return `${seconds} sec`;
    } else {
      return 'Now';
    }
  };

  const updateCountdown = () => {
    setTimelineData(prevData => {
      return prevData.map(alert => ({
        ...alert,
        time: calculateTimeLeft(alert),
      }));
    });
  };

  const setupNotifications = async () => {
    messaging()
      .getToken()
      .then(token => {
        registerDevice(token).catch(error => {
          console.log(error);
        });
      })
      .catch(error => {
        console.log(error);
      });
  };



  // This function will be used to apply filters consistently
  const applyFilter = useCallback((data: any[], filterType: string) => {
    if (!data || data.length === 0) {
      return [];
    }
    const now = new Date();
    const filteredData = data.filter((alert: any) => {
      if (
        !alert.alertDate ||
        !alert.expiryDate ||
        alert.defaultAlertTime == null
      ) {
        return false;
      }

      const alertDate = new Date(alert.alertDate);
      const expiryDate = new Date(alert.expiryDate);
      const nextAlertDate = new Date(alert.nextAlertDate);

      // Skip expired alerts
      if (expiryDate < now) {
        return false;
      }

      if (filterType === 'all') {
        // Return all non-expired alerts
        return true;
      }

      // Extract number of days from filterType e.g. '7days' -> 7
      const daysMatch = filterType.match(/^(\d+)days?$/);
      if (!daysMatch) {
        // If filterType format is unexpected, just return false
        return false;
      }

      const days = parseInt(daysMatch[1], 10);
      const endDate = new Date(now);
      endDate.setDate(endDate.getDate() + days);

      // Check if nextAlertDate is between now and endDate, and before expiryDate
      return (
        nextAlertDate >= now &&
        nextAlertDate <= endDate &&
        nextAlertDate <= expiryDate
      );
    });

    return filteredData;
  }, []);

  // Wrap fetchData in useCallback to prevent it from changing on every render
  // const async fetchData = useCallback(() => {
  //   // Set loading state to true before fetching data
  //   setIsLoading(true);
  //   // Clear existing data to avoid showing stale data
  //   setTimelineData([]);

  //   getAlerts(false)
  //     .then(newData => {
  //       // Make sure we have data before proceeding
  //       if (!newData || newData.length === 0) {
  //         setTimelineData([]);
  //         setIsError(false);
  //         setIsLoading(false);
  //         return;
  //       }
  //       setAllAlertData(newData);
  //       // Apply the current filter to the data
  //       const filteredData = applyFilter(newData, selectedFilter);

  //       // Map the filtered data to add colors based on status and calculate initial time
  //       const mappedData = filteredData.map((alert: any) => {
  //         const color =
  //           alert.status === 0
  //             ? themeStyles.primary
  //             : alert.status === 1
  //             ? themeStyles.amber
  //             : themeStyles.danger;
  //         return {
  //           ...alert,
  //           circleColor: color,
  //           lineColor: color,
  //           time: calculateTimeLeft(alert), // Calculate initial time to prevent flickering
  //         };
  //       });
  //       const pendingAlertId = await AsyncStorage.getItem('pendingAlertId');

  //     if (pendingAlertId) {

  //         checkPendingNotificationAlert(newData);
  //     }
  //       // Ensure state updates are applied in the correct order
  //       setTimelineData(mappedData as AlertModel[]);
  //       setIsError(false);

  //       // Check for pending notification alert after data is loaded

  //     })
  //     .catch(err => {
  //       setError(err);
  //       setIsError(true);
  //       // Clear timeline data on error to avoid showing stale data
  //       setTimelineData([]);
  //     })
  //     .finally(() => {
  //       setIsLoading(false);
  //       setRefreshing(false);
  //     });
  // }, [
  //   selectedFilter,
  //   applyFilter,
  //   getAlerts,
  //   themeStyles.primary,
  //   themeStyles.amber,
  //   themeStyles.danger,
  // ]);
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setTimelineData([]);

    try {
      const newData = await getAlerts(false);

      if (!newData || newData.length === 0) {
        setTimelineData([]);
        setIsError(false);
        return;
      }

      setAllAlertData(newData);
      const filteredData = applyFilter(newData, selectedFilter);

      const mappedData = filteredData.map((alert: any) => {
        const color =
          alert.status === 0
            ? themeStyles.primary
            : alert.status === 1
            ? themeStyles.amber
            : themeStyles.danger;

        return {
          ...alert,
          circleColor: color,
          lineColor: color,
          time: calculateTimeLeft(alert),
        };
      });

      // Check for pending notification alert after data is loaded
      const pendingAlertId = await AsyncStorage.getItem('pendingAlertId');
      if (pendingAlertId) {
        const showAlertData = mappedData.find(
          (alert: AlertModel) => alert.id === pendingAlertId,
        );
        if (showAlertData) {
          // Clear the pending alert ID
          await AsyncStorage.removeItem('pendingAlertId');

          // Set pending alert data and show flag (bypasses regular modal logic)
          setPendingAlertData(showAlertData);
          setShowPendingAlert(true);
        }
      }

      setTimelineData(mappedData as AlertModel[]);
      setIsError(false);
    } catch (err) {
      setError(err);
      setIsError(true);
      setTimelineData([]);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [
    selectedFilter,
    applyFilter,
    getAlerts,
    themeStyles.primary,
    themeStyles.amber,
    themeStyles.danger,
  ]);

  const fetchAboutMessage = async () => {
    getAboutMessage()
      .then(async message => {
        const isFirstTime = await AsyncStorage.getItem('SliderComplete');
        const alreadyHideAboutMsg = await AsyncStorage.getItem(
          'hideAboutMessage',
        );
        if (isFirstTime && !alreadyHideAboutMsg) {
          setModalVisible(true);
        } else {
          setModalVisible(message.showAboutMessage);
        }
        if (!message.showAboutMessage) {
          setShowNotificationsModal(
            message.loginCount % 5 == 0 && !message.allowPushNotifications,
          );
          registerThisDevice();
          getOutStandingAlerts();
        }
      })
      .catch(error => {
        console.log(error);
        setModalVisible(false);
      });
  };
  // Function to get and sort alerts
  const getOutStandingAlerts = () => {
    getOutstandingAlerts()
      .then(result => {
        console.log(result);
        if (result && result.length > 0) {
          const currentTime = new Date().getTime();
          // Filter out past alerts
          const validAlerts = result.filter((alert: any) => {
            return new Date(alert.nextAlertDate).getTime() >= currentTime;
          });

          if (validAlerts.length > 0) {
            const sortedAlerts = validAlerts.sort((a: any, b: any) => {
              const dateA = new Date(a.nextAlertDate).getTime();
              const dateB = new Date(b.nextAlertDate).getTime();
              return dateA - dateB;
            });
            const alertData = timelineData.find(
              alert => alert.categoryId === sortedAlerts[0].categoryId,
            );
            if (alertData) setCategoryName(alertData?.categoryName);
            setOutstandingAlerts(sortedAlerts);
            setCurrentAlertIndex(0);
          } else {
            setOutstandingAlerts([]);
          }
        } else {
          setOutstandingAlerts([]);
        }
      })
      .catch(error => {
        console.log('Error fetching outstanding alerts:', error);
      });
  };

  const onAlertCompleted = (completedAlertId: any) => {
    const updatedAlerts = outstandingAlerts.filter(
      alert => alert.id !== completedAlertId,
    );
    const sortedRemainingAlerts = updatedAlerts.sort((a, b) => {
      const dateA = new Date(a.nextAlertDate).getTime();
      const dateB = new Date(b.nextAlertDate).getTime();
      return dateA - dateB;
    });

    if (sortedRemainingAlerts.length > 0) {
      const alertData = timelineData.find(
        alert => alert.categoryId === sortedRemainingAlerts[0].categoryId,
      );
      if (alertData) {
        setCategoryName(alertData?.categoryName);
      }
    }

    setOutstandingAlerts(sortedRemainingAlerts);
    setAlertModalAvailable(true);
    setCurrentAlertIndex(0);
    getOutStandingAlerts();

    // Refresh timeline data to show updated countdown after alert dismissal
    fetchData();
  };

  const shouldShowAlertModal =
    (outstandingAlerts.length > 0 &&
    alertModalAvailable &&
    currentAlertIndex < outstandingAlerts.length) || showPendingAlert;

  // Get the current alert if there is one
  const currentAlert = showPendingAlert
    ? pendingAlertData
    : shouldShowAlertModal
    ? {
        ...outstandingAlerts[currentAlertIndex],
        categoryName:
          timelineData.find(
            t =>
              t.categoryId === outstandingAlerts[currentAlertIndex].categoryId,
          )?.categoryName || categoryName,
      }
    : null;



  const handleRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const addAlert = () => {
    navigation.navigate('Alert', {});
  };

  const actionAlert = (id: string) => {
    let alertDB = timelineData.find(x => x.id == id);
    if (alertDB) {
      navigation.navigate('Alert', {alertId: id});
    }
  };

  const renderDetail = (data: any) => {
    return <AlertListItem alert={data} onSelect={() => actionAlert(data.id)} />;
  };

  const toggleModal = () => {
    setModalVisible(false);
    hideAboutMessage();
    registerThisDevice();
    // uncomment this to show the notifications modal after 1 second
    // setTimeout(() => {
    //   setShowEnrollMFAModal(true);
    // }, 1000);
  };

  const registerThisDevice = () => {
    requestNotifications(['alert', 'badge']);
  };

  const onAlertActioned = () => {
    if (showPendingAlert) {
      // Handle pending alert completion
      setShowPendingAlert(false);
      setPendingAlertData(null);
    } else {
      // Handle regular alert completion
      setAlertModalAvailable(false);
    }
  };

  const toggleIsMFAEnabled = () => {
    setShowEnrollMFAModal(false);
    saveUserSetting('isMFAEnabled', true)
      .then(() => {})
      .catch(error => {
        console.log(error);
      });
  };

  // Effect to fetch data when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchData();
    });
    return unsubscribe;
  }, [navigation, selectedFilter, fetchData]);

  const filterAlertData = (filterValue: string) => {
    setTimelineData([]);
    setIsLoading(true);
    setSelectedFilter(filterValue);
    setShowFilterDropdown(false); // Close dropdown when filter is selected

    // Use setTimeout to show loading briefly, then apply filter
    setTimeout(() => {
      const filteredData = applyFilter(allAlertData, filterValue);
      const mappedData = filteredData.map((alert: AlertModel) => {
        const color =
          alert.status === 0
            ? themeStyles.primary
            : alert.status === 1
            ? themeStyles.amber
            : themeStyles.danger;
        return {
          ...alert,
          circleColor: color,
          lineColor: color,
          time: calculateTimeLeft(alert), // Calculate initial time to prevent flickering
        };
      });
      setTimelineData(mappedData);
      setIsLoading(false); // Set loading to false after filtering is complete
    }, 100); // Brief delay to show loading state
  };

  // Filter options for dropdown
  const filterOptions = [
    { key: 'all', label: 'All' },
    { key: '1day', label: '1 Day' },
    { key: '7days', label: '7 Days' },
    { key: '30days', label: '30 Days' },
    { key: '90days', label: '90 Days' },
  ];

  // Get current filter label
  const getCurrentFilterLabel = () => {
    const currentOption = filterOptions.find(option => option.key === selectedFilter);
    return currentOption ? currentOption.label : 'All';
  };

  return (
    <View style={[styles.container, {backgroundColor: themeStyles.background}]}>
      <Modal
        isVisible={isModalVisible}
        style={[
          styles.modalContainer,
          {backgroundColor: themeStyles.background},
        ]}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.modalContentContainer}>
          <View
            // eslint-disable-next-line react-native/no-inline-styles
            style={{
              flex: 0.5,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Image
              source={require('../assets/images/logo-dark.png')}
              style={{
                width: iconSize,
                height: iconSize * 0.5,
                backgroundColor: themeStyles.background,
              }}
            />
          </View>

          <View style={styles.modalContent}>
            <CheckingNotificationsIcon
              width={iconSize}
              height={iconSize}
              fill={themeStyles.background}
            />
            <Text style={[styles.title, {color: themeStyles.text}]}>
              Welcome to Flerts
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              This app was designed for you to never miss a deadline.
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              Get expert help and switch plans and save money.
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              Turn on notifications for the full experience.
            </Text>
            <TouchableOpacity
              style={[styles.cancel, {borderColor: themeStyles.text}]}
              onPress={toggleModal}>
              <Text style={[styles.cancelText, {color: themeStyles.text}]}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Modal>
      <Modal
        isVisible={showNotificationsModal}
        style={[
          styles.modalContainer,
          {backgroundColor: themeStyles.background},
        ]}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.modalContentContainer}>
          <View style={styles.modalContent}>
            <CheckingNotificationsIcon
              width={iconSize}
              height={iconSize}
              fill={themeStyles.background}
            />
            <Text style={[styles.title, {color: themeStyles.text}]}>
              Enable Notifications
            </Text>
            <Text style={[styles.description, {color: themeStyles.text}]}>
              Get the most out of Flerts by enabling push notifications so you
              never miss an alert
            </Text>
            {/* @ts-ignore */}
            <TouchableOpacity
              style={[styles.btn, {borderColor: themeStyles.primary}]}
              onPress={() => navigation.navigate('Settings')}>
              <Text style={[styles.btnText, {color: themeStyles.background}]}>
                OK
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cancel, {borderColor: themeStyles.text}]}
              onPress={() => setShowNotificationsModal(false)}>
              <Text style={[styles.cancelText, {color: themeStyles.text}]}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Modal>
      <EnrollMFAModal
        visible={showEnrollMFAModal}
        onComplete={toggleIsMFAEnabled}
        onCancel={() => setShowEnrollMFAModal(false)}
        startScreen="recommend_mfa"
      />
      {shouldShowAlertModal && currentAlert && (
        <AlertModal
          navigation={navigation}
          visible={true}
          alert={currentAlert}
          alertCompleted={() => onAlertCompleted(currentAlert.id)}
          onAlertActioned={onAlertActioned}
          bypassTimeCheck={showPendingAlert}
        />
      )}

      {/* Filter Section with Dropdown */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            {
              backgroundColor: theme === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.05)',
              borderColor: theme === 'dark'
                ? 'rgba(255, 255, 255, 0.2)'
                : 'rgba(0, 0, 0, 0.1)',
            },
          ]}
          onPress={() => setShowFilterDropdown(!showFilterDropdown)}
          activeOpacity={0.7}>
          <View style={styles.filterButtonContent}>
            <Text style={[styles.calendarIcon]}>📅</Text>
            <Text style={[styles.filterButtonText, {color: themeStyles.text}]}>
              {getCurrentFilterLabel()}
            </Text>
            <Text style={[styles.dropdownArrow, {color: themeStyles.text}]}>
              {showFilterDropdown ? '▲' : '▼'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Dropdown Menu */}
        {showFilterDropdown && (
          <View style={[
            styles.dropdownMenu,
            {
              backgroundColor: themeStyles.background,
              borderColor: theme === 'dark'
                ? 'rgba(255, 255, 255, 0.2)'
                : 'rgba(0, 0, 0, 0.1)',
              shadowColor: theme === 'dark' ? '#fff' : '#000',
            },
          ]}>
            {filterOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.dropdownItem,
                  selectedFilter === option.key && {
                    backgroundColor: themeStyles.primary,
                  },
                ]}
                onPress={() => filterAlertData(option.key)}
                activeOpacity={0.7}>
                <Text
                  style={[
                    styles.dropdownItemText,
                    {
                      color: selectedFilter === option.key
                        ? themeStyles.background
                        : themeStyles.text,
                      fontWeight: selectedFilter === option.key ? '600' : '400',
                    },
                  ]}>
                  {option.label}
                </Text>
                {selectedFilter === option.key && (
                  <Text style={[styles.checkmark, {color: themeStyles.background}]}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>

      {/* Content area that shows loading or alerts */}
      {isLoading ? (
        <Center>
          <ActivityIndicator color={themeStyles.primary} size="large" />
        </Center>
      ) : !isError ? (
        timelineData?.length > 0 ? (
          <Timeline
            style={styles.list}
            data={timelineData}
            timeContainerStyle={styles.timeContainerStyle}
            // eslint-disable-next-line react-native/no-inline-styles
            timeStyle={{
              textAlign: 'center',
              backgroundColor: themeStyles.background,
              color: themeStyles.text,
              padding: 5,
              borderRadius: 13,
              overflow: 'hidden',
              borderColor: themeStyles.text,
              borderWidth: 1,
            }}
            detailContainerStyle={{marginTop: 0}}
            listViewStyle={styles.listViewStyle}
            renderDetail={renderDetail}
            //@ts-ignore
            options={{
              showsVerticalScrollIndicator: false,
              showsHorizontalScrollIndicator: false,
              refreshControl: (
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                />
              ),
            }}
          />
        ) : (
          <ScrollView
            contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
              />
            }>
            <Center>
              <Text
                // eslint-disable-next-line react-native/no-inline-styles
                style={{
                  textAlignVertical: 'center',
                  textAlign: 'center',
                  paddingRight: 20,
                  color: themeStyles.text,
                }}>
                No alerts found for the selected filter. Try a different filter
                or tap the plus to create a new alert.
              </Text>
            </Center>
          </ScrollView>
        )
      ) : (
        <ErrorMessage onRefresh={handleRefresh} refreshing={refreshing} />
      )}
      {showFeedImage && (
        <Image
          source={require('../assets/images/urgent-tax.png')}
          style={styles.feedImage}
        />
      )}

      <TouchableOpacity
        style={[
          styles.plusIconButton,
          {
            backgroundColor: themeStyles.primary,
            width: plusIconButtonSize,
            height: plusIconButtonSize,
            borderRadius: plusIconButtonBorderRadius,
          },
        ]}
        onPress={addAlert}>
        <PlusIcon
          width={plusIconSize}
          height={plusIconSize}
          fill={themeStyles.background}
          style={styles.raised}
        />
      </TouchableOpacity>
    </View>
  );
};

const {width} = Dimensions.get('window');

// Calculate the percentage for the date container
const dateContainerWidth = width < 800 ? 100 : 300;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingLeft: 20,
    paddingTop: 5,
    position: 'relative',
  },
  groupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 2,
  },
  list: {
    flex: 1,
    marginTop: 10,
  },
  btn: {
    marginTop: 30,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancel: {
    marginTop: 30,
    marginBottom: 15,
    width: '100%',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  cancelText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#c5c5c5',
  },
  modalContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 15,
  },
  modalContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 25,
    marginBottom: 10,
  },
  description: {
    marginBottom: 10,
    textAlign: 'center',
  },
  middle: {
    justifyContent: 'center',
    alignContent: 'center',
    backgroundColor: 'red',
  },
  plusIconButton: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 20,
    bottom: 30,
    zIndex: 1000,
    elevation: 5,
  },
  feedImage: {
    position: 'absolute',
    bottom: 0,
    width: undefined,
    height: '30%',
    aspectRatio: 1,
    transform: [{scaleX: -1}],
  },
  raised: {
    shadowColor: 'black',
    shadowRadius: 2,
    shadowOpacity: 0.8,
    shadowOffset: {width: 1, height: 1},
  },
  timeContainerStyle: {
    minWidth: dateContainerWidth,
    maxWidth: dateContainerWidth,
  },
  listViewStyle: {
    marginTop: 0,
  },
  // Filter Dropdown Styles
  filterContainer: {
    marginVertical: 8,
    marginHorizontal: 16,
    position: 'relative',
    zIndex: 1000,
    alignSelf: 'flex-start',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
    minWidth: 120,
    maxWidth: 160,
  },
  filterButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  calendarIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  filterButtonText: {
    fontSize: 13,
    fontWeight: '500',
    flex: 1,
  },
  dropdownArrow: {
    fontSize: 10,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    marginTop: 2,
    borderRadius: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 6,
    zIndex: 1001,
    minWidth: 120,
    maxWidth: 160,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  dropdownItemText: {
    fontSize: 13,
    flex: 1,
  },
  checkmark: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
