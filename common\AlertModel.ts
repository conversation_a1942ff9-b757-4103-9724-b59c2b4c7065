import { JSXElementConstructor } from 'react'
import { IAlert, FileType as DocumentType } from "./IAlert";

export class AlertModel implements IAlert {
    id: string;
    categoryId:string;
    categoryName:string;
    subCategoryId:string;
    subCategoryName:string;
    time: string;
    isUrgent: boolean;
    policyNumber: string;
    companyName: string;
    expiryDate: Date;
    alertDate: Date;
    nextAlertDate: Date;
    defaultAlertTime: number;
    notes: string;
    circleColor: string;
    lineColor: string;
    documents: DocumentType[];
    isArchived: boolean;
    status: number;
    iconName: string;
    subCategoryIsRequired: boolean;
    showFeedImage: boolean;

    constructor(props: IAlert | undefined) {
        if (props) {
            this.id = props.id;
            this.categoryId = props.categoryId;
            this.categoryName = props.categoryName;
            this.subCategoryId = props.subCategoryId;
            this.subCategoryName = props.subCategoryName;
            this.time = props.time;
            this.isUrgent = props.isUrgent;
            this.policyNumber = props.policyNumber;
            this.companyName = props.companyName;
            this.expiryDate = props.expiryDate;
            this.nextAlertDate = props.nextAlertDate;
            this.alertDate = props.alertDate;
            this.defaultAlertTime = props.defaultAlertTime;
            this.notes = props.notes;
            this.circleColor = props.circleColor;
            this.lineColor = props.lineColor;
            this.documents = props.documents;
            this.isArchived = props.isArchived;
            this.status = props.status;
            this.iconName = props.iconName;
            this.subCategoryIsRequired = props.subCategoryIsRequired;
            this.showFeedImage = props.showFeedImage;
          } else {
            // Default values in case of undefined argument
            this.id = "";
            this.categoryId = "";
            this.categoryName = "";
            this.subCategoryId = "";
            this.subCategoryName = "";
            this.time = "";
            this.isUrgent = false;
            this.policyNumber = "";
            this.companyName = "";
            this.expiryDate = new Date();
            this.alertDate = new Date();
            this.nextAlertDate = new Date();
            this.defaultAlertTime = 7; // Default to 7 days
            this.notes = "";
            this.circleColor = "";
            this.lineColor = "";
            this.documents = [];
            this.isArchived = false;
            this.status = 0
            this.iconName = "";
            this.subCategoryIsRequired = false;
            this.showFeedImage = false
      }
    }
  }