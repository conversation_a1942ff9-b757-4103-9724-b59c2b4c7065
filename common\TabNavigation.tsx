import AsyncStorage from '@react-native-async-storage/async-storage';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {
  CopilotProvider,
  CopilotStep,
  useCopilot,
  walkthroughable,
} from 'react-native-copilot';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { BottomFabBar } from 'rn-wave-bottom-bar';
import PhoneIcon from '../assets/images/customer-service-svgrepo-com.svg';
import DocumentIcon from '../assets/images/file-solid.svg';
import GearIcon from '../assets/images/gear-solid.svg';
import HomeIcon from '../assets/images/house-solid.svg';
import ListIcon from '../assets/images/list-solid.svg';
import { useApi } from '../context/ApiContext';
import { useTheme } from '../context/ThemeContext';
import CategoriesScreen from '../screens/CategoriesScreen';
import DocumentsScreen from '../screens/DocumentsScreen';
import HomeScreen from '../screens/HomeScreen';
import SettingsScreen from '../screens/SettingsScreen';
import { RootStackParamList } from '../types';

const Tab = createBottomTabNavigator();
const WalkthroughableView = walkthroughable(View);
type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Login'
>;
type Props = {
  navigation: LoginScreenNavigationProp;
};
type Navigation = NavigationProp<RootStackParamList>;
const TabNavigationWithTooltip = () => {
  const { themeStyles, theme } = useTheme();
  const { getUserSettings } = useApi();
  const { start, copilotEvents } = useCopilot();
  const navigation = useNavigation<Navigation>();

  const [userInitials, setUserInitials] = useState('F');
  const [isLoading, setIsLoading] = useState(true);
  const [onboardingComplete, setOnboardingComplete] = useState(false);

  const [isCopilotStarted, setIsCopilotStarted] = useState(false);
  const [windowDimensions, setWindowDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  });
  const inputIconSize = 32;
  const windowHeight = Dimensions.get('window').height;

  useEffect(() => {
    const fetchUserSettings = async () => {
      const result = await getUserSettings();
      const initials = result.firstName[0] + result.surname[0];
      await AsyncStorage.setItem('userPhoneNumber', result.phoneNo ? result.phoneNo : '');
      setUserInitials(initials.toUpperCase());
    };

    const checkOnboarding = async () => {
      const onboardingStatus = await AsyncStorage.getItem('onboardingComplete');
      setOnboardingComplete(!!onboardingStatus);

      if (!onboardingStatus && !isCopilotStarted) {
        setTimeout(() => {
          start();
          setIsCopilotStarted(true);
        }, 500);
      }
      setIsLoading(false);
    };

    fetchUserSettings();
    checkOnboarding();
  }, [getUserSettings, start]);

  useEffect(() => {
    const fetchUserSettings = async () => {
      const result = await getUserSettings();
      const initials = result.firstName[0] + result.surname[0];
      setUserInitials(initials.toUpperCase());
    };
    const unObserve = navigation.addListener('focus', () => {
      fetchUserSettings();
    });
    return () => {
      unObserve();
    };
  }, [navigation]);

  useEffect(() => {
    const handleEvent = () => {
      if (copilotEvents) {
        const onStop = async () => {
          await AsyncStorage.setItem('onboardingComplete', 'true');
        };
        copilotEvents.on('stop', onStop);

        return () => {
          copilotEvents.off('stop', onStop);
        };
      }
    };

    handleEvent();
  }, [copilotEvents]);

  useEffect(() => {
    const handleResize = () => {
      setWindowDimensions({
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
      });
    };

    const subscription = Dimensions.addEventListener('change', handleResize);

    return () => {
      subscription?.remove();
    };
  }, []);

  const customBoxHeight = windowDimensions.height * 0.07;
  const customBoxWidth = windowDimensions.width * 0.2;
  const translateYDynamic = Platform.OS === 'ios' ? 0 : windowDimensions.height * 0.05;
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={themeStyles.primary} />
      </View>
    );
  }

  return (
    <>
      <Tab.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: themeStyles.background,
            shadowColor: '#000',
          },
          headerTintColor: themeStyles.primary,
          headerTitleAlign: 'center',
          tabBarActiveTintColor: themeStyles.white,
          tabBarActiveBackgroundColor: themeStyles.white,
          tabBarInactiveBackgroundColor: 'red',
        }}
        tabBar={props => (
          <BottomFabBar
            mode={'square'}
            isRtl={false}
            focusedButtonStyle={{
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 7},
              shadowOpacity: 0.41,
              shadowRadius: 9.11,
              elevation: 14,
            }}
            bottomBarContainerStyle={{backgroundColor: themeStyles.background}}
            {...props}
          />
        )}>
        <Tab.Screen
          name="Home"
          component={HomeScreen}
          options={{
            tabBarIcon: () => (
              <CopilotStep
                text="This is your home tab!"
                order={2}
                name="homeTab">
                <WalkthroughableView
                  style={[
                    styles.customStepBox,
                    {
                      height: windowDimensions.height * 0.08,
                      width: windowDimensions.width * 0.18,
                    },
                    {transform: [{translateY: translateYDynamic}]},
                  ]}>
                  <HomeIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={
                      theme === 'dark'
                        ? themeStyles.color
                        : themeStyles.primary
                    }
                    style={[
                      styles.raised,
                      {transform: [{translateY: -translateYDynamic}]},
                      {},
                    ]}
                  />
                </WalkthroughableView>
              </CopilotStep>
            ),
            headerStyle: {
              backgroundColor: themeStyles.background,
              height:
                Platform.OS == 'ios' ? 100 : windowDimensions.height * 0.08,
              shadowColor: '#000',
            },

            headerLeft: () => (
              <TouchableOpacity onPress={() => navigation.navigate('Profile')}>
                <Text
                  style={{
                    marginLeft: 20,
                    color: themeStyles.primary,
                    fontWeight: '900',
                    fontSize: 32,
                  }}>
                  {userInitials}
                </Text>
              </TouchableOpacity>
            ),
            headerRight: () => (
              <TouchableOpacity
                style={{marginRight: 20}}
                onPress={() => navigation.navigate('Expert')}>
                <CopilotStep
                  text="Tap here to ask an expert for help!"
                  order={1}
                  name="expertTab">
                  <WalkthroughableView
                    style={[
                      styles.customStepBox,
                      {
                        height: 30,
                        width: windowDimensions.width * 0.13,
                        marginRight: -10,
                      },
                      {transform: [{translateY: translateYDynamic}]},
                    ]}>
                    <PhoneIcon
                      width={27}
                      height={27}
                      fill={themeStyles.primary}
                      style={[
                        styles.raised,
                        {transform: [{translateY: -translateYDynamic}]},
                      ]}
                    />
                  </WalkthroughableView>
                </CopilotStep>
              </TouchableOpacity>
            ),
            headerTitle: () => (
              <Image
                source={require('../assets/images/logo-dark.png')}
                style={{
                  width: 120,
                  height: 60,
                  resizeMode: 'contain',
                }}
              />
            ),
          }}
        />

        <Tab.Screen
          name="Documents"
          component={DocumentsScreen}
          options={{
            tabBarIcon: () => (
              <CopilotStep
                text="Here you can manage your documents!"
                order={3}
                name="documentsTab">
                <WalkthroughableView
                  style={[
                    styles.customStepBox,
                    {height: customBoxHeight, width: customBoxWidth},
                    {transform: [{translateY: translateYDynamic}]},
                  ]}>
                  <DocumentIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={
                      theme === 'dark' ? themeStyles.color : themeStyles.primary
                    }
                    style={[
                      styles.raised,
                      {transform: [{translateY: -translateYDynamic}]},
                    ]}
                  />
                </WalkthroughableView>
              </CopilotStep>
            ),
          }}
        />

        <Tab.Screen
          name="Categories"
          component={CategoriesScreen}
          options={{
            tabBarIcon: () => (
              <CopilotStep
                text="Browse your categories here!"
                order={4}
                name="categoriesTab">
                <WalkthroughableView
                  style={[
                    styles.customStepBox,
                    {height: customBoxHeight, width: customBoxWidth},
                    {transform: [{translateY: translateYDynamic}]},
                  ]}>
                  <ListIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={
                      theme === 'dark' ? themeStyles.color : themeStyles.primary
                    }
                    style={[
                      styles.raised,
                      {transform: [{translateY: -translateYDynamic}]},
                    ]}
                  />
                </WalkthroughableView>
              </CopilotStep>
            ),
          }}
        />

        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarIcon: () => (
              <CopilotStep
                text="Adjust your settings here!"
                order={5}
                name="settingsTab">
                <WalkthroughableView
                  style={[
                    styles.customStepBox,
                    {height: customBoxHeight, width: customBoxWidth},
                    {transform: [{translateY: translateYDynamic}]},
                  ]}>
                  <GearIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={
                      theme === 'dark' ? themeStyles.color : themeStyles.primary
                    }
                    style={[
                      styles.raised,
                      {transform: [{translateY: -translateYDynamic}]},
                    ]}
                  />
                </WalkthroughableView>
              </CopilotStep>
            ),
          }}
        />
      </Tab.Navigator>
    </>
  );
};

const styles = StyleSheet.create({
  raised: {
    shadowColor: 'black',
    shadowRadius: 2,
    shadowOpacity: 0.8,
    shadowOffset: { width: 1, height: 1 },
    elevation: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customStepBox: {
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
});

const TabNavigation = () => {
  return (
    <CopilotProvider>
      <TabNavigationWithTooltip />
    </CopilotProvider>
  );
};

export default TabNavigation;
