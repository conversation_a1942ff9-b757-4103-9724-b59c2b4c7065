import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Keyboard,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  CameraOptions,
  ImageLibraryOptions,
  launchCamera,
  launchImageLibrary,
} from 'react-native-image-picker';
import { PERMISSIONS, RESULTS, check, request } from 'react-native-permissions';
import { CategoryType } from '../api/types/CategoryType';
import AlertIcon from '../assets/images/bell-solid.svg';
import CalendarIcon from '../assets/images/calendar-days-regular.svg';
import CameraIcon from '../assets/images/camera-solid.svg';
import SaveIcon from '../assets/images/floppy-disk-solid.svg';
import ImagesIcon from '../assets/images/images-solid.svg';
import DeleteIcon from '../assets/images/trash-solid.svg';
import CrossIcon from '../assets/images/x-solid.svg';
import { AlertModel } from '../common/AlertModel';
import { generateGuid } from '../common/GuidGenerator';
import { FileType } from '../common/IAlert';
import { ListItem } from '../common/ListItem';
import {
  FieldValidation,
  ValidateField,
  ValidateForm,
} from '../common/Validation';
import { formatDate } from '../common/helpers';
import ConfirmModal from '../components/ConfirmModal';
import Document from '../components/Document';
import ListModal from '../components/ListModal';
import WarningModal from '../components/WarningModal';
import { useApi } from '../context/ApiContext';
import { useRefresh } from '../context/RefreshContex';
import { useTheme } from '../context/ThemeContext';
import { RootStackParamList } from '../types';

type RouteParams = {
  alertId?: string;
  restore?: boolean;
  actioned?: boolean;
  categoryId?: string;
  subCategoryId?: string;
};

type AlertScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Alert'
>;

type Props = {
  navigation: AlertScreenNavigationProp;
};

export type ImageType = {
  id: string;
  name: string;
  type: string;
  uri: string;
};

const AlertScreen: React.FC<Props> = ({ navigation }) => {
  const route = useRoute();
  const { alertId, restore, actioned, categoryId, subCategoryId }: RouteParams =
    route.params || {};


  const {
    getAlert,
    createAlert,
    updateAlert,
    archiveAlert,
    deleteAlert,
    getCategories,
    getSubCategories,
    saveDocuments,
  } = useApi();

  const { themeStyles, theme } = useTheme();
  const { refreshScreens } = useRefresh();

  const [showExpiryDatePicker, setExpiryShowDatePicker] = useState(false);
  const [showAlertDatePicker, setAlertShowDatePicker] = useState(false);
  const [showAlertTimePicker, setAlertShowTimePicker] = useState(false);
  const [showCategoryList, setShowCategoryList] = useState(false);
  const [showSubCategoryList, setShowSubCategoryList] = useState(false);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [subCategories, setSubCategories] = useState<CategoryType[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<ListItem[]>([]);
  const [subCategoryOptions, setSubCategoryOptions] = useState<ListItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [images, setImages] = useState<ImageType[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [showCameraWarningModal, setShowCameraWarningModal] =
    useState<boolean>(false);
  const [showCompanyName, setShowCompanyName] = useState(false);

  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [form, setForm] = useState<AlertModel>({
    id: '',
    categoryId: '',
    categoryName: '',
    subCategoryId: '',
    subCategoryName: '',
    time: '',
    isUrgent: false,
    policyNumber: '',
    companyName: '',
    expiryDate: new Date(new Date().setHours(23, 59, 59, 999)),
    alertDate: new Date(),
    nextAlertDate: new Date(),
    notes: '',
    circleColor: themeStyles.primary,
    lineColor: themeStyles.primary,
    documents: [],
    isArchived: false,
    status: 0,
    iconName: '',
    subCategoryIsRequired: false,
    showFeedImage: false,
  });

  const [validation, setValidation] = useState({
    id: '',
    categoryId: '',
    categoryName: '',
    subCategoryId: '',
    subCategoryName: '',
    time: '',
    isUrgent: '',
    policyNumber: '',
    companyName: '',
    expiryDate: '',
    alertDate: '',
    notes: '',
    documents: '',
  });

  const windowWidth = Dimensions.get('window').width;
  const inputIconSize = 16;

  useEffect(() => {

    if (alertId) {
      setIsLoading(true);
      var today = new Date();
      var tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      getAlert(alertId)
        .then(alert => {
          let alertModel = new AlertModel({
            id: actioned ? '' : alert.id,
            categoryId: alert.categoryId,
            categoryName: alert.categoryName,
            subCategoryId: alert.subCategoryId,
            subCategoryName: alert.subCategoryName,
            time: alert.time,
            isUrgent: alert.isUrgent,
            policyNumber: alert.policyNumber,
            companyName: alert.companyName,
            expiryDate: restore ? tomorrow : new Date(alert.expiryDate),
            alertDate: restore ? tomorrow : new Date(alert.alertDate),
            nextAlertDate: restore ? tomorrow : new Date(alert.nextAlertDate),
            notes: alert.notes,
            circleColor: alert.circleColor,
            lineColor: alert.lineColor,
            documents: actioned ? [] : alert.documents,
            isArchived: false,
            status: alert.status,
            iconName: alert.iconName,
            subCategoryIsRequired: false,
            showFeedImage: alert.showFeedImage,
          });

          setForm(alertModel);
          setIsFormValid(ValidateForm(alertModel, validationSchema));
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          setIsLoading(false);
          fetchCategories();
        });
    } else {
      setForm({
        ...form,
        categoryId: categoryId ? categoryId : '',
        subCategoryId: subCategoryId ? subCategoryId : '',
      });
      setIsLoading(false);
      fetchCategories();
    }
  }, [alertId]);

  useEffect(() => {
    if (form.categoryId) {
      var index = categoryOptions
        .map(category => {
          return category.id;
        })
        .indexOf(form.categoryId);

      if (index > -1) {
        setForm({ ...form, categoryName: categoryOptions[index].name });
        fetchSubCategories(form.categoryId);
      }
    }
  }, [categoryOptions]);

  useEffect(() => {
    if (form.categoryId) {
      var index = subCategoryOptions
        .map(category => {
          return category.id;
        })
        .indexOf(form.subCategoryId);

      var newForm = {
        ...form,
        subCategoryIsRequired: subCategoryOptions.length > 0,
      };

      if (index > -1) {
        newForm.subCategoryName = subCategoryOptions[index].name;
      }

      setForm(newForm);
      setIsFormValid(ValidateForm(newForm, validationSchema));
    }
  }, [subCategoryOptions]);

  useEffect(() => {
    var showCompanyName = false;

    if (
      form.categoryName != 'Events' &&
      form.subCategoryName != 'Tax' &&
      form.subCategoryName != 'MOT' &&
      form.subCategoryName != 'Driving License' &&
      form.subCategoryName != 'Passport' &&
      form.subCategoryName != 'Vaccination' &&
      !categories.find(c => c.categoryId == categoryId)?.isCustomCategory
    ) {
      showCompanyName = true;
    }

    setShowCompanyName(showCompanyName);
  }, [form.categoryName, form.subCategoryName, categories, categoryId]);

  const fetchCategories = () => {
    getCategories(false)
      .then(result => {
        setCategories(result);
        let options: ListItem[] = result.map((category: CategoryType) => {
          return { id: category.categoryId, name: category.name };
        });
        setCategoryOptions(options);
      })
      .catch(error => {
        console.log(error);
      });
  };

  const fetchSubCategories = (categoryKey: string) => {
    getSubCategories(categoryKey, false)
      .then(result => {
        setSubCategories(result);

        if (result.length > 0) {
          let options: ListItem[] = result.map((category: CategoryType) => {
            return { id: category.subCategoryId, name: category.name };
          });
          setSubCategoryOptions(options);

          var newForm = { ...form };

          if (form.subCategoryId) {
            var index = options
              .map(category => {
                return category.id;
              })
              .indexOf(form.subCategoryId);
            if (index > -1) {
              newForm.subCategoryName = options[index].name;
            }
          }

          setForm(newForm);
        } else {
          setSubCategoryOptions([]);
        }
      })
      .catch(error => {
        console.log(error);
      });
  };

  const validationSchema: FieldValidation[] = [
    {
      name: 'categoryId',
      requirements: ['Required'],
      displayName: 'Category',
    },
    {
      name: 'subCategoryId',
      requirements: ['RequiredIf'],
      displayName: 'Subcategory',
      options: {
        field: 'subCategoryIsRequired',
        value: true,
        operator: 'equal',
      },
    },
    {
      name: 'policyNumber',
      requirements: ['RequiredOr'],
      displayName: 'Reference number',
      fields: [{
        fieldName: 'policyNumber',
        displayName: 'Reference number'
      }, {
        fieldName: 'companyName',
        displayName: 'Company name'
      }]
    },
    {
      name: 'companyName',
      requirements: ['RequiredOr'],
      displayName: 'Company name',
      fields: [
        {
          fieldName: 'policyNumber',
          displayName: 'Reference number'
        }, {
          fieldName: 'companyName',
          displayName: 'Company name'
        }
      ]
    },

  ];

  const onValueChange = (name: string, value: any) => {
    let newForm: AlertModel;
    if (name == 'categoryId') {
      newForm = {
        ...form,
        categoryId: value.id,
        categoryName: value.name,
        subCategoryId: '',
        subCategoryName: '',
      };

      showCategoryList ? setShowCategoryList(false) : setShowCategoryList(true);

      fetchSubCategories(newForm.categoryId);
    } else if (name == 'subCategoryId') {
      newForm = {
        ...form,
        subCategoryId: value.id,
        subCategoryName: value.name,
      };

      showSubCategoryList
        ? setShowSubCategoryList(false)
        : setShowSubCategoryList(true);
    } else if (name == 'alertDate' || name == 'expiryDate') {
      newForm = {
        ...form,
        [name]: new Date(value),
      };

      if (name == 'alertDate') {
        newForm.nextAlertDate = new Date(value);
        // Only auto-update expiry date for new alerts (no alertId)
        if (!alertId) {
          newForm.expiryDate = new Date(new Date(value).setHours(23, 59, 59, 999))
        }
      }
    } else {
      newForm = { ...form, [name]: value };
    }

    setForm(newForm);

    var showCompanyName = false;
    if (
      newForm.categoryName != 'Events' &&
      newForm.subCategoryName != 'Tax' &&
      newForm.subCategoryName != 'MOT' &&
      newForm.subCategoryName != 'Driving License' &&
      newForm.subCategoryName != 'Passport' &&
      newForm.subCategoryName != 'Vaccination' &&
      !categories.find(c => c.categoryId == newForm.categoryId)?.isCustomCategory
    ) {
      showCompanyName = true;
    } else {
      // Only clear company name if it's a new alert (no alertId) AND the change is category/subcategory related
      if (!alertId && (name === 'categoryId' || name === 'subCategoryId')) {
        newForm.companyName = '';
        setForm(newForm); // Update form with cleared company name
      }
    }

    setShowCompanyName(showCompanyName);

    if (new Date(newForm.expiryDate).getTime() < new Date(newForm.alertDate).getTime()) {
      setIsFormValid(false);
    } else {
      setIsFormValid(ValidateForm(newForm, validationSchema));
    }
  };

  const onFieldBlur = (name: string, value: any | undefined) => {
    let schema = validationSchema.filter(x => x.name == name);

    if (schema.length > 0) {
      let error = '';

      var formToValidate = { ...form };
      if (value) {
        //@ts-ignore
        formToValidate[name] = value;
      }

      error = ValidateField(formToValidate, schema[0]);
      if (!form.policyNumber && !form.companyName) {
        setValidation({
          ...validation,
          policyNumber: 'Either Reference or Company Name is required.',
          companyName: 'Either Reference or Company Name is required.',
        });
      } else {
        setValidation({
          ...validation,
          policyNumber: '',
          companyName: '',
        });
      }

      console.log('error', error);


      if (name === 'policyNumber' || name === 'companyName') {
        if (error) {
          setValidation({ ...validation, [name]: error });
        } else {
          setValidation({ ...validation, policyNumber: '', companyName: '' });
        }
      }

      if (name == 'expiryDate') {
        setValidation({ ...validation, [name]: error, alertDate: '' });
      } else {
        if (name == 'alertDate') {
          if (new Date(form.expiryDate) < new Date(value)) {
            error = 'Product expiration date must be before expiry date';
          }
        }

        // setValidation({ ...validation, [name]: error });
      }
    }
  };

  const handleChange = (name: string, value: string) => {

    if (name == 'expiryDate' || name == 'alertDate') {
      setForm(prevFormData => ({
        ...prevFormData,
        [name]: new Date(value),
      }));
    } else {
      setForm(prevFormData => ({
        ...prevFormData,
        [name]: value,
      }));
    }
  };


  const createNewAlert = () => {
    setIsLoading(true);
    form.nextAlertDate = form.alertDate;
    console.log(form)
    createAlert(form)
      .then(result => {
        saveDocuments(result.id, images)
          .then(() => {
            setIsError(false);
          })
          .catch(error => {
            console.log(error);
            setIsError(true);
          });
        navigation.navigate('Home', { refresh: true });
      })
      .catch(error => {
        console.log('Error saving alert ', error);
        setIsError(true);
      })
      .finally(() => {
        setIsLoading(false);
        refreshScreens(['Home', 'Documents']);
      });
  };

  const saveCurrentAlert = () => {
    setIsLoading(true);
    form.nextAlertDate = form.alertDate;
    updateAlert(form)
      .then(result => {
        saveDocuments(form.id, images)
          .then(() => {
            navigation.navigate('Home', { refresh: true });
            setIsError(false);
          })
          .catch(error => {
            console.log(error);
            setIsError(true);
          });
      })
      .catch(error => {
        setIsError(true);
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
        refreshScreens(['Home', 'Documents']);
      });
  };

  const onArchive = () => {
    setShowDeleteModal(true);
  };

  const onConfirmArchive = () => {
    archiveAlert(form.id)
      .then(() => {
        navigation.navigate('Home', { refresh: true });
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        setShowDeleteModal(false);
        refreshScreens(['Home', 'Documents']);
      });
  };

  const onDeleteAlertWithoutArchive = () => {
    deleteAlert(form.id).then(() => {
      refreshScreens(['Home', 'Documents']);
      navigation.navigate('Home', { refresh: true });
    });
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const showExpiryDatePickerDate = () => {
    if (showExpiryDatePicker) {
      setExpiryShowDatePicker(false);
    } else {
      setExpiryShowDatePicker(true);
    }
  };

  const handleExpiryDateChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date | undefined,
  ) => {
    setExpiryShowDatePicker(false);

    if (selectedDate !== undefined) {
      selectedDate.setHours(23, 59, 59, 999);
      onValueChange('expiryDate', selectedDate.toString());
      onFieldBlur('expiryDate', selectedDate);
      let defaultAlertTime: number = 30;

      if (form.subCategoryId == '') {
        let category = categories.find(x => {
          return x.categoryId === form.categoryId;
        });

        if (category) {
          defaultAlertTime = category.defaultAlertTime;
        }
      } else {
        //@ts-ignore
        defaultAlertTime = subCategories.find(x => {
          return x.subCategoryId === form.subCategoryId;
        }).defaultAlertTime;
      }

      let alertDate = new Date(selectedDate);
      alertDate.setDate(alertDate.getDate() - defaultAlertTime);

      if (alertDate < new Date()) {
        alertDate = new Date(Date.now());
        alertDate.setDate(alertDate.getDate());
      }

      let newForm = {
        ...form,
        expiryDate: new Date(selectedDate),
      };
      setForm(newForm);
    }
  };

  const handleAlertDateChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date | undefined,
  ) => {
    setAlertShowDatePicker(false);

    if (selectedDate !== undefined) {
      onValueChange('alertDate', selectedDate.toString());

      onFieldBlur('alertDate', selectedDate);
      // onValueChange('expiryDate',new Date(selectedDate).setHours(23,59,59,999).toString())

      setAlertShowTimePicker(true);
    }
  };

  const showAlertDatePickerDate = () => {
    if (showAlertDatePicker) {
      setAlertShowDatePicker(false);
    } else {
      setAlertShowDatePicker(true);
    }
  };

  const toggleCategoryList = () => {
    showCategoryList ? setShowCategoryList(false) : setShowCategoryList(true);
  };

  const handleCatItemSelect = (item: ListItem) => {
    onValueChange('categoryId', item);
  };

  const toggleSubCategoryList = () => {
    showSubCategoryList
      ? setShowSubCategoryList(false)
      : setShowSubCategoryList(true);
  };

  const handleSubItemSelect = (item: ListItem) => {
    onValueChange('subCategoryId', item);
  };

  const toggleCamera = async () => {
    let cameraIsAllowed: boolean = false;

    let permission =
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.CAMERA
        : PERMISSIONS.IOS.CAMERA;
    check(permission)
      .then(result => {
        switch (result) {
          case RESULTS.UNAVAILABLE:
            return;
          case RESULTS.DENIED:
            request(permission)
              .then(result => {
                switch (result) {
                  case RESULTS.LIMITED:
                  case RESULTS.GRANTED:
                    cameraIsAllowed = true;
                    break;
                  case RESULTS.DENIED:
                  case RESULTS.BLOCKED:
                    setShowCameraWarningModal(true);
                    return;
                }
              })
              .catch(error => {
                console.log(error);
              });
            break;
          case RESULTS.LIMITED:
          case RESULTS.GRANTED:
            cameraIsAllowed = true;
            console.log('The permission is granted');
            break;
          case RESULTS.BLOCKED:
            setShowCameraWarningModal(true);
            return;
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        if (cameraIsAllowed) {
          let options: CameraOptions = {
            mediaType: 'photo',
            saveToPhotos: true,
            includeBase64: true,
          };
          launchCamera(options, cameraCallback);
        }
      });
  };

  const cameraCallback = (result: any) => {
    if (result && result.assets && result.assets.length > 0) {
      const id = generateGuid();

      const temp: ImageType = {
        id: id,
        name: result.assets[0].fileName,
        type: result.assets[0].type,
        uri: result.assets[0].uri,
      };

      setImages([...images, temp]);
      setForm({
        ...form,
        documents: [
          ...form.documents,
          { id: id, fileName: result.assets[0].uri },
        ],
      });
    }
  };

  const toggleImageLibrary = () => {
    let libraryIsAllowed: boolean = false;

    if (Platform.OS !== 'android') {
      let permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
      check(permission)
        .then(result => {
          switch (result) {
            case RESULTS.UNAVAILABLE:
              console.log('Camera is not available on this device');
              return;
            case RESULTS.DENIED:
              console.log('The permission is requestable');
              request(permission)
                .then(result => {
                  switch (result) {
                    case RESULTS.LIMITED:
                    case RESULTS.GRANTED:
                      libraryIsAllowed = true;
                      break;
                    case RESULTS.DENIED:
                    case RESULTS.BLOCKED:
                      setShowCameraWarningModal(true);
                      return;
                  }
                })
                .catch(error => {
                  console.log(error);
                });
              break;
            case RESULTS.LIMITED:
            case RESULTS.GRANTED:
              libraryIsAllowed = true;
              console.log('The permission is granted');
              break;
            case RESULTS.BLOCKED:
              setShowCameraWarningModal(true);
              return;
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          if (libraryIsAllowed) {
            displayImageLibrary();
          }
        });
    } else {
      displayImageLibrary();
    }
  };

  const displayImageLibrary = () => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      selectionLimit: 0,
    };
    launchImageLibrary(options, libraryCallback);
  };

  const libraryCallback = (result: any) => {
    if (
      result &&
      !result.didCancel &&
      result.assets &&
      result.assets.length > 0
    ) {
      const imagesToAdd: ImageType[] = [];
      const formDataToAdd: FileType[] = [];

      result.assets.forEach((image: any) => {
        const id = generateGuid();

        const temp: ImageType = {
          id: id,
          name: image.fileName,
          type: image.type,
          uri: image.uri,
        };

        imagesToAdd.push(temp);
        formDataToAdd.push({ id: id, fileName: image.uri });
      });

      setImages([...images, ...imagesToAdd]);
      setForm({ ...form, documents: [...form.documents, ...formDataToAdd] });
    }
  };

  const deletePhoto = (idToDelete: string) => {
    const updatedImages = [...images];
    const updatedFormData = [...form.documents];

    const imageToRemove = updatedImages
      .map(function (x) {
        return x.id;
      })
      .indexOf(idToDelete);
    const formDataImageToRemove = updatedFormData
      .map(function (x) {
        return x.id;
      })
      .indexOf(idToDelete);

    if (imageToRemove > -1) {
      updatedImages.splice(imageToRemove, 1);
    }

    if (formDataImageToRemove > -1) {
      updatedFormData.splice(formDataImageToRemove, 1);
    }

    setImages(updatedImages);
    setForm({ ...form, documents: updatedFormData });
  };
  const handleAlertTimeChange = (
    event: DateTimePickerEvent,
    selectedTime?: Date | undefined,
  ) => {
    setAlertShowTimePicker(false);

    if (selectedTime !== undefined) {
      const updatedDate = new Date(form.alertDate);
      updatedDate.setHours(selectedTime.getHours());
      updatedDate.setMinutes(selectedTime.getMinutes());

      setForm(prevFormData => ({
        ...prevFormData,
        alertDate: updatedDate,
      }));
      onFieldBlur('alertDate', updatedDate);
    }
  };
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color={themeStyles.primary} />
      </View>
    );
  }

  return (
    // <SafeAreaView >
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100,
        backgroundColor: themeStyles.background,
      }}
      keyboardShouldPersistTaps="handled"
      scrollEnabled={true}
      showsVerticalScrollIndicator={false}>
      <TouchableWithoutFeedback onPress={() => dismissKeyboard()}>
        <View style={styles.childContainer}>
          {/* <ConfirmModal visible={showDeleteModal} question={"Are you sure you want to archive this alert?"} onConfirm={onConfirmArchive} onReject={() => setShowDeleteModal(false)} /> */}
          <ConfirmModal
            visible={showDeleteModal}
            onConfirm={onConfirmArchive}
            onReject={onDeleteAlertWithoutArchive}
            question="Do you want to archive this alert?"
            onBack={() => setShowDeleteModal(false)}
          />
          <WarningModal
            visible={isError}
            warning="Unable to save alert"
            onConfirm={() => setIsError(false)}
          />
          <WarningModal
            visible={showCameraWarningModal}
            warning="Flerts does not have access to the camera, it can be enabled in your device settings"
            onConfirm={() => setShowCameraWarningModal(false)}
          />
          <View style={styles.categoryDropdown}>
            <Text style={[styles.label, {color: themeStyles.text}]}>
              Category
            </Text>
            <TouchableOpacity
              onPress={() => toggleCategoryList()}
              style={[
                styles.inputDropdown,
                {backgroundColor: themeStyles.background},
              ]}>
              <Text
                style={[styles.inputDropdownLabel, {color: themeStyles.text}]}>
                {form.categoryName ? form.categoryName : 'Select one'}
              </Text>
            </TouchableOpacity>
            <ListModal
              dataList={categoryOptions}
              visible={showCategoryList}
              onClose={toggleCategoryList}
              onItemSelect={handleCatItemSelect}
              selectedItem={form.categoryId}
            />
          </View>
          {subCategories.length > 0 && (
            <View style={styles.subCategoryDropdown}>
              <Text style={[styles.label, {color: themeStyles.text}]}>
                Description
              </Text>
              <TouchableOpacity
                onPress={() => toggleSubCategoryList()}
                style={[
                  styles.inputDropdown,
                  {backgroundColor: themeStyles.background},
                ]}>
                <Text
                  style={[
                    styles.inputDropdownLabel,
                    {color: themeStyles.text},
                  ]}>
                  {form.subCategoryName ? form.subCategoryName : 'Select one'}
                </Text>
              </TouchableOpacity>
              <ListModal
                dataList={subCategoryOptions}
                visible={showSubCategoryList}
                onClose={toggleSubCategoryList}
                onItemSelect={handleSubItemSelect}
                selectedItem={form.subCategoryId}
              />
            </View>
          )}
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Reference
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
              },
            ]}
            onChangeText={value => onValueChange('policyNumber', value)}
            value={form.policyNumber}
            placeholder={form.policyNumber ? '' : 'i.e. 857451'}
            placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
            onBlur={() => onFieldBlur('policyNumber', undefined)}
          />
          {validation.policyNumber != '' && (
            <Text style={styles.validationError}>
              {validation.policyNumber}
            </Text>
          )}
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Company Name
          </Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
              },
            ]}
            onChangeText={value => onValueChange('companyName', value)}
            value={form.companyName}
            placeholder={form.companyName ? '' : 'Enter Company name'}
            placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
            onBlur={() => onFieldBlur('companyName', undefined)}
          />
          {validation.companyName != '' && (
            <Text style={styles.validationError}>{validation.companyName}</Text>
          )}
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Note to self
          </Text>
          <TextInput
            style={[
              styles.inputNotes,
              {
                color: themeStyles.text,
                backgroundColor: themeStyles.background,
              },
            ]}
            onChangeText={value => handleChange('notes', value)}
            value={form.notes}
            placeholder={form.notes ? '' : 'i.e. Birthday Event'}
            placeholderTextColor={theme === 'dark' ? '#fff' : 'gray'}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Expiry Date
          </Text>
          <View style={styles.groupContainer}>
            {Platform.OS === 'ios' ? (
              <View style={{width: 130}}>
                <DateTimePicker
                  value={form.expiryDate}
                  mode="date"
                  display="default"
                  minimumDate={
                    new Date(new Date(form.alertDate).setHours(23, 59, 59, 999))
                  }
                  onChange={handleExpiryDateChange}
                  themeVariant={theme}
                />
              </View>
            ) : (
              <>
                <TextInput
                  style={[
                    styles.input,
                    styles.sideBySide,
                    styles.center,
                    {
                      color: themeStyles.text,
                      backgroundColor: themeStyles.background,
                    },
                  ]}
                  value={formatDate(form.expiryDate)}
                  editable={false}
                />
                <TouchableOpacity
                  style={[
                    styles.btn,
                    styles.sideBySide,
                    styles.btnDate,
                    {backgroundColor: themeStyles.primary},
                  ]}
                  onPress={() => showExpiryDatePickerDate()}>
                  <View style={styles.iconContainer}>
                    <CalendarIcon
                      width={inputIconSize}
                      height={inputIconSize}
                      fill={themeStyles.background}
                    />
                    <Text
                      style={[
                        styles.iconText,
                        {color: themeStyles.background},
                      ]}>
                      Date
                    </Text>
                  </View>
                </TouchableOpacity>
                {showExpiryDatePicker && (
                  <DateTimePicker
                    value={form.expiryDate}
                    minimumDate={
                      new Date(
                        new Date(form.alertDate).setHours(23, 59, 59, 999),
                      )
                    }
                    mode="date"
                    display="default"
                    onChange={handleExpiryDateChange}
                  />
                )}
              </>
            )}
          </View>
          {validation.expiryDate != '' && (
            <Text style={styles.validationError}>{validation.expiryDate}</Text>
          )}
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Alert Date
          </Text>
          <View style={styles.groupContainer}>
            {Platform.OS === 'ios' ? (
              <View style={styles.dateTimePickerContainer}>
                <DateTimePicker
                  minimumDate={form.alertDate}
                  value={form.alertDate}
                  mode="datetime"
                  display="default"
                  onChange={handleAlertDateChange}
                  themeVariant={theme}
                />
              </View>
            ) : (
              <>
                <TextInput
                  style={[
                    styles.input,
                    styles.sideBySide,
                    styles.center,
                    {
                      color: themeStyles.text,
                      backgroundColor: themeStyles.background,
                    },
                  ]}
                  value={formatDate(form.alertDate, 'alert')}
                  editable={false}
                />
                <TouchableOpacity
                  style={[
                    styles.btn,
                    styles.sideBySide,
                    styles.btnDate,
                    {backgroundColor: themeStyles.primary},
                  ]}
                  onPress={() => showAlertDatePickerDate()}>
                  <View style={styles.iconContainer}>
                    <CalendarIcon
                      width={inputIconSize}
                      height={inputIconSize}
                      fill={themeStyles.background}
                    />
                    <Text
                      style={[
                        styles.iconText,
                        {color: themeStyles.background},
                      ]}>
                      Date
                    </Text>
                  </View>
                </TouchableOpacity>
                {showAlertDatePicker && (
                  <DateTimePicker
                    value={form.alertDate}
                    // minimumDate={new Date()}
                    mode="date"
                    display="default"
                    onChange={handleAlertDateChange}
                  />
                )}
                {showAlertTimePicker && (
                  <DateTimePicker
                    value={form.alertDate}
                    mode="time"
                    display="default"
                    onChange={handleAlertTimeChange}
                  />
                )}
              </>
            )}
          </View>
          {validation.alertDate != '' && (
            <Text style={styles.validationError}>{validation.alertDate}</Text>
          )}
          <Text style={[styles.label, {color: themeStyles.text}]}>
            Documents
          </Text>

          <View style={styles.groupContainer}>
            <TouchableOpacity
              style={[
                styles.btnSideBySide,
                {backgroundColor: themeStyles.primary},
              ]}
              onPress={toggleCamera}>
              <View style={styles.iconContainer}>
                <CameraIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  fill={themeStyles.background}
                />
                <Text
                  style={[styles.iconText, {color: themeStyles.background}]}>
                  Camera
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.btnSideBySide,
                {
                  backgroundColor: themeStyles.background,
                  borderWidth: 2,
                  borderColor: themeStyles.primary,
                },
              ]}
              onPress={toggleImageLibrary}>
              <View style={styles.iconContainer}>
                <ImagesIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  fill={themeStyles.primary}
                />
                <Text style={[styles.iconText, {color: themeStyles.primary}]}>
                  Image library
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          <View>
            {form.documents.map((image, index) => (
              <View style={styles.previewPhoto} key={index}>
                <Document
                  documentName={image.fileName}
                  width={200}
                  height={200}
                />
                <TouchableOpacity
                  style={[
                    {
                      backgroundColor: themeStyles.danger,
                      borderWidth: 2,
                      borderColor: themeStyles.danger,
                      position: 'absolute',
                      top: 0,
                      right: 50,
                      padding: 2,
                      borderRadius: 50,
                    },
                  ]}
                  onPress={() => deletePhoto(image.id)}>
                  <CrossIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.background}
                  />
                </TouchableOpacity>
              </View>
            ))}
          </View>
          <TouchableOpacity
            style={
              isFormValid
                ? [styles.btn, {backgroundColor: themeStyles.primary}]
                : [
                    styles.btnDisabled,
                    {backgroundColor: themeStyles.primaryDisabled},
                  ]
            }
            onPress={
              !form.id || form.id == '' ? createNewAlert : saveCurrentAlert
            }
            disabled={!isFormValid}>
            {isLoading ? (
              <ActivityIndicator color={themeStyles.background} />
            ) : (
              <View style={styles.iconContainer}>
                {!form.id || form.id == '' ? (
                  <AlertIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.background}
                  />
                ) : (
                  <SaveIcon
                    width={inputIconSize}
                    height={inputIconSize}
                    fill={themeStyles.background}
                  />
                )}
                <Text
                  style={[styles.iconText, {color: themeStyles.background}]}>
                  {!form.id || form.id == '' ? 'Add Alert' : 'Save'}
                </Text>
              </View>
            )}
          </TouchableOpacity>
          {form.id != '' && !restore ? (
            <TouchableOpacity
              style={[styles.archive, {borderColor: themeStyles.danger}]}
              onPress={onArchive}>
              <View style={styles.iconContainer}>
                <DeleteIcon
                  width={inputIconSize}
                  height={inputIconSize}
                  fill={themeStyles.danger}
                />
                <Text
                  style={[styles.archiveIconText, {color: themeStyles.danger}]}>
                  Delete
                </Text>
              </View>
            </TouchableOpacity>
          ) : null}
        </View>
      </TouchableWithoutFeedback>
    </ScrollView>
    // </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: 1,
  },
  childContainer: {
    marginTop: 10,
    padding: 20,
  },
  groupContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    marginTop: 4,
  },
  label: {
    marginTop: 10,
    zIndex: -1,
  },
  inputDropdownLabel: {
    marginTop: 10,
    color: '#000',
  },
  inputDropdown: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginVertical: 10,
    zIndex: -1,
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginVertical: 10,
    zIndex: -1,
  },
  center: {
    textAlign: 'center',
  },
  sideBySide: {
    width: '45%',
  },
  inputNotes: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 10,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 5,
    margin: 10,
    minHeight: 100,
  },
  btnDate: {
    marginTop: 12,
  },
  btnSideBySide: {
    marginTop: 20,
    marginBottom: 15,
    width: '48%',
    borderRadius: 25,
    height: 40,
  },
  btn: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#108a00',
    padding: 12,
    borderRadius: 25,
    height: 40,
  },
  btnDisabled: {
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 25,
    height: 40,
  },
  btnText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  archive: {
    marginTop: 10,
    marginBottom: 15,
    width: '100%',
    borderWidth: 2,
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  categoryDropdown: {
    zIndex: 2,
  },
  subCategoryDropdown: {
    zIndex: 1,
  },
  iconContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -2,
  },
  iconText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  archiveIconText: {
    color: '#767676',
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: 10,
  },
  image: {
    width: 200,
    height: 200,
    margin: 5,
  },
  previewPhoto: {
    alignItems: 'center',
  },
  validationError: {
    marginTop: 5,
    marginLeft: 10,
    color: '#767676',
    fontWeight: 'bold',
  },
  dateTimePickerContainer: {
    width: Dimensions.get('window').width / 1.75,
    // justifyContent: 'space-between', // Ensures equal spacing
    // alignItems: 'center',
    // marginVertical: 10,
  },
  pickerWrapper: {
    flex: 1, // Allows equal width to both pickers
    marginHorizontal: 5, // Spacing between pickers
  },
  dateTimePicker: {
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AlertScreen;
