import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Keyboard,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  ImagePickerResponse,
  launchImageLibrary,
} from 'react-native-image-picker';
import { PERMISSIONS, RESULTS, check, request } from 'react-native-permissions';
import EditIcon from '../assets/images/icons8-edit.svg';
import { generateGuid } from '../common/GuidGenerator';
import { UserProfile } from '../common/UserModel';
import WarningModal from '../components/WarningModal';
import { useApi } from '../context/ApiContext';
import { useTheme } from '../context/ThemeContext';
import { ImageType } from './AlertScreen';

interface IUserForm {
  firstName?: string;
  surname?: string;
  phoneNo?: string
}

const ProfileScreen: React.FC = () => {
  const { themeStyles } = useTheme();
  const [showCameraWarningModal, setShowCameraWarningModal] =
    useState<boolean>(false);
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const { getUserSettings, saveProfileImage, getDocument, updateUser } = useApi();
  const [image, setImage] = useState<string | null>(null);
  const [form, setForm] = useState<IUserForm>({
    firstName: '',
    surname: '',
    phoneNo: ''
  });
  const [errors, setErrors] = useState<IUserForm>({
    firstName: '',
    surname: '',
    phoneNo: ''
  });

  const fetchProfilePicture = (fileName: string) => {
    if (fileName) {
      if (fileName.includes('file:///')) setImage(fileName);
      else {
        setUploading(true);
        getDocument(fileName)
          .then((result: string) => {
            setImage(result);
          })
          .catch(error => {
            console.error(error);
            Alert.alert(`${error}`);
            setLoading(false)
            setUploading(false);
          })
          .finally(() => {
            setLoading(false)
            setUploading(false);
          });
      }
    }
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const onUpload = () => {
    let libraryIsAllowed: boolean = false;

    if (Platform.OS !== 'android') {
      let permission = PERMISSIONS.IOS.PHOTO_LIBRARY;
      check(permission)
        .then(result => {
          switch (result) {
            case RESULTS.UNAVAILABLE:
              console.log('Camera is not available on this device');
              return;
            case RESULTS.DENIED:
              console.log('The permission is requestable');
              request(permission)
                .then(result => {
                  switch (result) {
                    case RESULTS.LIMITED:
                    case RESULTS.GRANTED:
                      libraryIsAllowed = true;
                      break;
                    case RESULTS.DENIED:
                    case RESULTS.BLOCKED:
                      setShowCameraWarningModal(true);
                      return;
                  }
                })
                .catch(error => {
                  console.log(error);
                });
              break;
            case RESULTS.LIMITED:
            case RESULTS.GRANTED:
              libraryIsAllowed = true;
              console.log('The permission is granted');
              break;
            case RESULTS.BLOCKED:
              setShowCameraWarningModal(true);
              return;
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          if (libraryIsAllowed) {
            uploadProfilePicture();
          }
        });
    } else {
      uploadProfilePicture();
    }
  };

  const uploadProfilePicture = async () => {
    try {
      const result: ImagePickerResponse = await launchImageLibrary({
        mediaType: 'photo',
        selectionLimit: 1,
      });
      if (
        !result.didCancel &&
        result?.assets?.[0]?.fileName &&
        result.assets[0].type &&
        result.assets[0].uri
      ) {
        const id = generateGuid();
        const temp: ImageType = {
          id,
          name: result.assets[0].fileName,
          type: result.assets[0].type,
          uri: result.assets[0].uri,
        };

        setUploading(true);
        const status = await saveProfileImage(temp);
        if (status) {
          Alert.alert('Uploaded successfully.');
          setImage(temp.uri);
        } else {
          Alert.alert('Upload failed.');
        }
      }
    } catch (error) {
      Alert.alert(`${error}`);
      console.error(error);
    } finally {
      setUploading(false);
    }
  };

  const fetchUserSettings = async () => {
    setLoading(true);
    try {
      const result: UserProfile = await getUserSettings();
      setUserData({ ...result, email: result.email});
      await AsyncStorage.setItem('userPhoneNumber', result.phoneNo ? result.phoneNo : '');
      setForm({
        firstName: result.firstName,
        surname: result.surname,
        phoneNo: result.phoneNo,
      });
      if (result?.document) {
        fetchProfilePicture(result.document.fileName);
      } else {
        const user = auth().currentUser;
        setImage(user?.photoURL ?? null);
      }
    } catch (error) {
      Alert.alert('Error fetching user settings', `${error}`);
    } finally {

      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setForm(prevForm => ({ ...prevForm, [field]: value }));
    setErrors(prevErrors => ({ ...prevErrors, [field]: '' }));
  };

  const validateForm = () => {
    let isValid = true;
    let newErrors: IUserForm = {
      firstName: '',
      surname: '',
      phoneNo: ''
    };

    if (!form.firstName) {
      newErrors.firstName = 'First name is required.';
      isValid = false;
    }
    if (!form.surname) {
      newErrors.surname = 'Last name is required.';
      isValid = false;
    }
    if (form.phoneNo && !/^\+?[1-9]\d{9,14}$/.test(form.phoneNo)) {
      newErrors.phoneNo =
        'Phone number must be valid and can include a country code.';
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const onSave = async () => {
    try {
      if (validateForm() && userData) {
        setLoading(true);
        const reqBody = { ...userData, ...form };
        if (reqBody.id) delete reqBody.id;
        const updatedUser: UserProfile = await updateUser(reqBody);
        setUserData(() => updatedUser);
        setForm({
          firstName: updatedUser.firstName,
          surname: updatedUser.surname,
          phoneNo: updatedUser.phoneNo
        });
        await AsyncStorage.setItem('userPhoneNumber', updatedUser.phoneNo ? updatedUser.phoneNo : '');
        Alert.alert('Profile updated successfully.');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('Profile update failed.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserSettings();
  }, []);

  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100,
        backgroundColor: themeStyles.background,
      }}
      keyboardShouldPersistTaps="handled"
      scrollEnabled={true}
      showsVerticalScrollIndicator={false}>
      {loading ? (
        <View
          style={{
            height: Dimensions.get('window').height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <ActivityIndicator color={themeStyles.primary} size="large" />
        </View>
      ) : userData ? (
        <TouchableWithoutFeedback onPress={() => dismissKeyboard()}>
          <View
            style={[
              styles.container,
              { backgroundColor: themeStyles.background },
            ]}>
            <WarningModal
              visible={showCameraWarningModal}
              warning="Flerts does not have access to the camera, it can be enabled in your device settings"
              onConfirm={() => setShowCameraWarningModal(false)}
            />
            <View style={{ position: 'relative' }}>
              <View
                style={{
                  width: 200,
                  height: 200,
                  borderRadius: 100,
                  borderColor: themeStyles.primaryDisabled,
                  borderWidth: 4,
                  overflow: 'hidden',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {uploading ? (
                  <ActivityIndicator color={themeStyles.primary} size="large" />
                ) : image ? (
                  <Image
                    source={{ uri: image }}
                    style={{ height: 200, width: 200 }}
                  />
                ) : (
                  <Text
                    style={{
                      backgroundColor: themeStyles.background,
                      fontSize: 80,
                      color: themeStyles.primary,
                      fontWeight: '700',
                    }}>
                    {userData.firstName==''?"F":userData.firstName?.charAt(0)}
                    {userData.surname?.charAt(0)}
                  </Text>
                )}
              </View>
              <TouchableOpacity
                style={[
                  {
                    backgroundColor: themeStyles.primary,
                    width: 40,
                    height: 40,
                    borderRadius: 6,
                    position: 'absolute',
                    right: 10,
                    bottom: 15,
                    zIndex: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                ]}
                onPress={onUpload}>
                <EditIcon
                  fill={themeStyles.background}
                  style={{
                    shadowColor: 'black',
                    shadowRadius: 2,
                    shadowOpacity: 0.8,
                    shadowOffset: { width: 1, height: 1 },
                  }}
                />
              </TouchableOpacity>
            </View>
            <View style={{ paddingTop: 20, paddingBottom: 8 }}>
              <View
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    textAlign: 'center',
                    fontWeight: 'bold',
                    color: themeStyles.inputText,
                    fontSize: 38,
                  }}>
                  {`${userData.firstName} ${userData.surname}`}
                </Text>
              </View>
              <Text
                style={{
                  textAlign: 'center',
                  color: themeStyles.text,
                  paddingVertical: 10,
                  fontSize: 18,
                }}>
                {userData.email.includes('@') ? userData.email : "" }
              </Text>
            </View>

            <Text style={[styles.label, { color: themeStyles.text }]}>
              First Name
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: themeStyles.text,
                  backgroundColor: themeStyles.background,
                },
              ]}
              onChangeText={value => handleInputChange('firstName', value)}
              value={form.firstName}
              placeholder="i.e. John"
              placeholderTextColor={themeStyles.text}
            />
            {errors.firstName ? (
              <Text style={[styles.error, { color: themeStyles.danger }]}>
                {errors.firstName}
              </Text>
            ) : null}
            <Text style={[styles.label, { color: themeStyles.text }]}>
              Last Name
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: themeStyles.text,
                  backgroundColor: themeStyles.background,
                },
              ]}
              onChangeText={value => handleInputChange('surname', value)}
              value={form.surname}
              placeholder="i.e. Doe"
              placeholderTextColor={themeStyles.text}
            />
            {errors.surname ? (
              <Text style={[styles.error, { color: themeStyles.danger }]}>
                {errors.surname}
              </Text>
            ) : null}
            <Text style={[styles.label, { color: themeStyles.text }]}>Phone</Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: themeStyles.text,
                  backgroundColor: themeStyles.background,
                },
              ]}
              onChangeText={value => handleInputChange('phoneNo', value)}
              value={form.phoneNo}
              placeholder="i.e. 9874561230"
              placeholderTextColor={themeStyles.text}
              keyboardType="phone-pad"
              maxLength={15}
            />
            {errors.phoneNo ? (
              <Text style={[styles.error, { color: themeStyles.danger }]}>
                {errors.phoneNo}
              </Text>
            ) : null}

            <TouchableOpacity style={styles.saveButton} onPress={onSave}>
              <Text style={styles.editButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      ) : (
        <Text>No user, please login again.</Text>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  label: {
    marginTop: 10,
    textAlign: 'left',
    width: '100%',
  },
  profilePic: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 20,
    backgroundColor: '#ccc',
  },
  saveButton: {
    marginTop: 15,
    marginBottom: 10,
    width: '100%',
    backgroundColor: '#158c4e',
    padding: 12,
    borderRadius: 25,
    height: 45,
  },
  editButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    marginBottom: 30,
  },
  editButtonText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    paddingHorizontal: 15,
    color: '#000',
    backgroundColor: '#fff',
    borderRadius: 25,
    marginBottom: 15,
    marginTop: 10,
    zIndex: -1,
    width: '100%',
  },
  error: {
    color: 'red',
    fontSize: 12,
    marginTop: -10,
    marginBottom: 10,
    paddingLeft: 10,
    alignSelf: 'flex-start',
  },
});

export default ProfileScreen;
