import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import TabNavigation from '../common/TabNavigation';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import AlertScreen from '../screens/AlertScreen';
import ArchiveScreen from '../screens/ArchiveScreen';
import CategoryScreen from '../screens/CategoryScreen';
import CustomerSupportScreen from '../screens/CustomerSupportScreen';
import DocumentDisplayScreen from '../screens/DocumentDisplayScreen';
import EmailSentScreen from '../screens/EmailSentScreen';
import ExpertScreen from '../screens/ExpertScreen';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import IntroSliderScreen from '../screens/IntroSliderScreen';
import LoginScreen from '../screens/LoginScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SignUpScreen from '../screens/SignUpScreen';
import SubCategoriesScreen from '../screens/SubCategoriesScreen';
import SubCategoryScreen from '../screens/SubCategoryScreen';
import VerificationCodeScreen from '../screens/VerificationCodeScreen';
import VerifyEmailScreen from '../screens/VerifyEmailScreen';
const Stack = createNativeStackNavigator();

export default function Routing(): JSX.Element {
  const {accessToken} = useAuth();

  return (
    <NavigationContainer>
      {accessToken != null ? <ProtectedRoutes /> : <UnprotectedRoutes />}
    </NavigationContainer>
  );
}

function ProtectedRoutes(): JSX.Element {
  const {themeStyles} = useTheme();

  const {emailVerified, isProviderLogin,signout} = useAuth();


useEffect(() => {
  (async () => {
    const isFirstLaunch = await AsyncStorage.getItem('hasLaunched');
    if (!isFirstLaunch && Platform.OS ==='ios') {
      await AsyncStorage.setItem('hasLaunched', 'true');
      await signout(); // Force clear auth on first launch
    }
  })();
}, []);

  return (
    <>
      <Stack.Navigator>
        {emailVerified || isProviderLogin ? (
          <>
            <Stack.Screen
              name="Login"
              component={TabNavigation}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name="SignedIn"
              component={TabNavigation}
              options={{headerShown: false}}
            />
            <Stack.Screen
              name="Alert"
              component={AlertScreen}
              options={{
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
                // headerShown:false,
              }}
            />
            <Stack.Screen
              name="Expert"
              component={ExpertScreen}
              options={{
                title: 'Ask an expert',
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="Profile"
              component={ProfileScreen}
              options={{
                title: 'Check Your Profile',
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="Document"
              component={DocumentDisplayScreen}
              options={{
                title: '',
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="SubCategories"
              component={SubCategoriesScreen}
              options={{
                title: 'Descriptions',
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="Archive"
              component={ArchiveScreen}
              options={{
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="Live Support"
              component={CustomerSupportScreen}
              options={{
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="Category"
              component={CategoryScreen}
              options={{
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="SubCategory"
              component={SubCategoryScreen}
              options={{
                title: 'Description',
                headerStyle: {backgroundColor: themeStyles.background},
                headerTintColor: themeStyles.primary,
                headerTitleAlign: 'center',
              }}
            />
            <Stack.Screen
              name="EmailSent"
              component={EmailSentScreen}
              options={{headerShown: false}}
            />
          </>
        ) : (
          <Stack.Screen
            name="VerifyEmail"
            component={VerifyEmailScreen}
            options={{headerShown: false}}
          />
        )}
      </Stack.Navigator>
    </>
  );
}

function UnprotectedRoutes(): JSX.Element {
  const [isFirstTime, setIsFirstTime] = useState(false);
  const onCompleteSlider = async () => {
    try {
      await AsyncStorage.setItem('SliderComplete', 'true');
    } catch (error) {
      console.log(error);
    }
    setIsFirstTime(false);
  };

  useEffect(() => {
    try {
      AsyncStorage.getItem('SliderComplete').then(value => {
        if (value == null || !value) {
          setIsFirstTime(true);
        } else {
          setIsFirstTime(false);
        }
      });
    } catch (error) {
      console.log(error);
    }
  }, []);

  return (
    <Stack.Navigator>
      {isFirstTime ? (
        <Stack.Screen name="Slider" options={{headerShown: false}}>
          {props => (
            <IntroSliderScreen {...props} onCompleteSlider={onCompleteSlider} />
          )}
        </Stack.Screen>
      ) : (
        <>
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="VerificationCode"
            component={VerificationCodeScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="SignUp"
            component={SignUpScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="ForgotPassword"
            component={ForgotPasswordScreen}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="EmailSent"
            component={EmailSentScreen}
            options={{headerShown: false}}
          />
        </>
      )}
    </Stack.Navigator>
  );
}
